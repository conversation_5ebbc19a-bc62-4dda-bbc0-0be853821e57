<template>
  <div class="container" style="height: 100%; padding: 24px">
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 16 },
      }"
      style="max-width: 600px"
    >
      <a-form-model-item label="调度方案名称" prop="caseName">
        <a-input v-model="form.caseName" placeholder="请输入" allow-clear />
      </a-form-model-item>
      
      <a-form-model-item label="来水方案" prop="waterFcstId">
        <a-select
          v-model="form.waterFcstId"
          placeholder="请选择来水方案"
          show-search
          option-filter-prop="children"
          :options="waterFcstOptions"
          @change="changeWaterFcst"
        />
      </a-form-model-item>
      
      <a-form-model-item label="调度方式" prop="dispatchMethod">
        <a-radio-group
          v-model="form.dispatchMethod"
          style="margin-top: 5px"
          @change="changeDispatchMethod"
        >
          <a-radio v-for="(el, idx) in dispatchMethodOptions" :key="idx" :value="el.value">
            {{ el.label }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      
      <a-form-model-item label="模型应用场景" prop="scene">
        <a-radio-group
          v-model="form.scene"
          style="margin-top: 5px"
          @change="changeScene"
        >
          <a-radio v-for="(el, idx) in sceneOptions" :key="idx" :value="el.value">
            {{ el.label }}场景
            <a-tooltip v-if="el.value === 2">
              <template slot="title">可对未来72h的数据进行预报模拟</template>
              ⓘ
            </a-tooltip>
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      
      <a-form-model-item label="开始时间" prop="startTime">
        <a-date-picker
          v-model="form.startTime"
          :disabled="form.scene === 2"
          format="YYYY-MM-DD HH:mm"
          valueFormat="YYYY-MM-DD HH:00"
          :show-time="{ format: 'HH:mm' }"
          :disabled-date="disabledStartDate"
          placeholder="请选择开始时间"
        />
      </a-form-model-item>
      
      <a-form-model-item label="结束时间" prop="endTime">
        <a-date-picker
          v-model="form.endTime"
          format="YYYY-MM-DD HH:00"
          valueFormat="YYYY-MM-DD HH:00"
          :show-time="{ format: 'HH:00' }"
          :disabled-date="disabledEndDate"
          placeholder="请选择结束时间"
        />
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
  import moment from 'moment'
  // import { getInWaterPage } from '../../../incoming-water-model/services'
  import { sceneOptions } from '../../config'

  // 调度方式选项
  const dispatchMethodOptions = [
    { label: '现状调度', value: 1 },
    { label: '推荐调度', value: 2 },
    { label: '手动调度', value: 3 },
  ]

  export default {
    name: 'BasicInfo',
    props: ['inWaterEchoData'],
    data() {
      return {
        waterFcstOptions: [],
        dispatchMethodOptions,
        sceneOptions,
        form: {
          caseName: undefined,
          waterFcstId: this.inWaterEchoData?.inWaterId || undefined,
          dispatchMethod: 2,
          scene: this.inWaterEchoData?.scene || 2, // 默认选择未来预报场景
          startTime: this.inWaterEchoData?.startTime || undefined,
          endTime: this.inWaterEchoData?.endTime || undefined,
        },
        rules: {
          caseName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          waterFcstId: [{ required: true, message: '来水方案不能为空', trigger: 'change' }],
          dispatchMethod: [{ required: true, message: '调度方式不能为空', trigger: 'change' }],
          scene: [{ required: true, message: '模型应用场景不能为空', trigger: 'change' }],
          startTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
          endTime: [{ required: true, message: '结束时间不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      // 模拟测试数据
      this.waterFcstOptions = [
        { label: '来水方案1', value: 1, scene: 1, startTime: '2024-01-01 08:00', endTime: '2024-01-03 20:00' },
        { label: '来水方案2', value: 2, scene: 2, startTime: '2024-01-02 08:00', endTime: '2024-01-04 20:00' },
        { label: '来水方案3', value: 3, scene: 1, startTime: '2024-01-03 08:00', endTime: '2024-01-05 20:00' },
      ]

      // 如果没有预设的来水方案，自动选择第一条
      if (!this.form.waterFcstId && this.waterFcstOptions.length > 0) {
        this.form.waterFcstId = this.waterFcstOptions[0].value
        // 触发来水方案变更事件，自动填充相关字段（标记为自动选择，不覆盖默认场景）
        this.changeWaterFcst(this.form.waterFcstId, true)
      }

      // getInWaterPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
      //   this.waterFcstOptions = (res.data?.data || []).map(el => ({ ...el, label: el.caseName, value: el.inWaterId }))
      //   // 如果没有预设的来水方案，自动选择第一条
      //   if (!this.form.waterFcstId && this.waterFcstOptions.length > 0) {
      //     this.form.waterFcstId = this.waterFcstOptions[0].value
      //     // 触发来水方案变更事件，自动填充相关字段（标记为自动选择，不覆盖默认场景）
      //     this.changeWaterFcst(this.form.waterFcstId, true)
      //   }
      // })
    },
    mounted() {
      // 如果默认选择了未来预报场景，设置默认时间
      if (this.form.scene === 2 && !this.form.startTime && !this.form.endTime) {
        this.form.startTime = moment().add(1, 'hours').format('YYYY-MM-DD HH:00')
        this.form.endTime = moment().add(3, 'days').format('YYYY-MM-DD HH:00')
      }
    },
    methods: {
      changeWaterFcst(val, isAutoSelect = false) {
        const obj = this.waterFcstOptions.find(el => el.value === val)

        // 如果是自动选择且已有默认场景值，则不覆盖场景
        if (!isAutoSelect || !this.form.scene) {
          this.form.scene = obj?.scene
        }

        // 如果是自动选择且当前场景是未来预报(2)，则不覆盖时间，让mounted中的逻辑处理
        if (!isAutoSelect || this.form.scene !== 2) {
          this.form.startTime = obj?.startTime
          this.form.endTime = obj?.endTime
        }

        this.$nextTick(() => {
          this.$refs.form.validateField('scene')
          this.$refs.form.validateField('startTime')
          this.$refs.form.validateField('endTime')
        })
      },

      changeDispatchMethod() {
        // 调度方式改变时的处理逻辑
      },

      changeScene(val) {
        if (val.target.value === 2) {
          this.form.startTime = moment().add(1, 'hours').format('YYYY-MM-DD HH:00')
          this.form.endTime = moment().add(3, 'days').format('YYYY-MM-DD HH:00')
          this.$refs.form.validateField('startTime')
          this.$refs.form.validateField('endTime')
        } else {
          this.form.startTime = undefined
          this.form.endTime = undefined
        }
      },
      
      disabledStartDate(current) {
        if (this.form.scene === 1) {
          if (this.form.endTime) {
            return (
              current < moment(this.form.endTime).subtract(15, 'days') ||
              current > moment(this.form.endTime) ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          return current < moment().subtract(1, 'days')
        }
      },
      
      disabledEndDate(current) {
        if (this.form.scene === 1) {
          if (this.form.startTime) {
            return (
              current < moment(this.form.startTime) ||
              current > moment(this.form.startTime).add(15, 'days') ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          return current < moment().subtract(1, 'days') || current > moment().add(3, 'days')
        }
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$emit('saveData', { ...this.form })
          } else {
            this.$emit('saveData', false)
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>

