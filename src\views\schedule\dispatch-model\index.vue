<template>
    <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff; padding: 24px">
        <!-- 第一大区块 - 顶部 -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <div>
                <a-select v-model="selectedScheme" style="width: 350px;" placeholder="请选择调度方案">
                    <a-select-option v-for="item in schemeOptions" :key="item.value" :value="item.value">
                        {{ item.label }}
                    </a-select-option>
                </a-select>
            </div>
            <div>
                <a-button type="primary" style="color: #fff; background: #165DFF; font-size: 14px; font-weight: 400;" @click="goToSchemeManage">
                    方案管理
                </a-button>
            </div>
        </div>

        <!-- 第二大区块 - 中间标题和tab -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <div>
                <h3 style="margin: 0; font-size: 20px; color: #1D2129; font-weight: 600;">水库调度模型</h3>
            </div>
            <div>
                <a-radio-group v-model="activeTab" button-style="solid">
                    <a-radio-button value="overview">方案概览</a-radio-button>
                    <a-radio-button value="detail">方案详情</a-radio-button>
                </a-radio-group>
            </div>
        </div>

        <!-- 第三大区块 - 底部内容 -->
        <div style="display: flex; height: calc(100vh - 200px);">
            <!-- 左侧信息展示 - 占30% -->
            <div class="info-panel" style="width: 30%; margin-right: 16px; border-radius: 8px; overflow: hidden;">
                <!-- 来水方案名称区块 -->
                <div class="water-scheme">
                    <h4 class="block-title">来水方案名称</h4>
                    <div class="scheme-info">
                        <div class="info-item full-width">
                            <span class="label">来水方案名称:</span>
                            <span class="value">{{ dataSource?.schemeName || '自动预报-桃花江灌区-2025051917~2025052216' }}</span>
                        </div>
                        <div class="info-item full-width">
                            <span class="label">预报时段:</span>
                            <span class="value">{{ dataSource?.forecastPeriod || '2025-05-21 16:00 ~ 2025-05-24 16:00' }}</span>
                        </div>
                        <div class="info-cards">
                            <div
                                v-for="card in waterSchemeCards"
                                :key="card.key"
                                class="info-card"
                                :style="{ background: card.background }"
                            >
                                <div class="card-header">
                                    <i class="icon" :class="card.iconClass"></i>
                                    <span class="card-title">{{ card.title }}</span>
                                </div>
                                <div class="card-content">
                                    <span class="card-value">{{ dataSource?.[card.dataKey] || card.defaultValue }}</span>
                                    <span class="card-unit">{{ card.unit }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 出库信息区块（仅概览tab显示） -->
                <div class="outflow-info">
                    <h4 class="block-title">出库信息</h4>
                    <div
                        v-for="(cardGroup, groupIndex) in outflowInfoCardGroups"
                        :key="groupIndex"
                        class="info-cards info-cards-bottom"
                    >
                        <div
                            v-for="card in cardGroup"
                            :key="card.key"
                            class="info-card"
                            :style="{
                                background: card.background,
                                visibility: card.hidden ? 'hidden' : 'visible'
                            }"
                        >
                            <div class="card-header">
                                <i class="icon" :class="card.iconClass"></i>
                                <span class="card-title">{{ card.title }}</span>
                            </div>
                            <div class="card-content">
                                <span class="card-value">{{ card.hidden ? '' : (dataSource?.[card.dataKey] || card.defaultValue) }}</span>
                                <span class="card-unit">{{ card.hidden ? '' : card.unit }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右侧内容区块 -->
            <div style="width: 70%; display: flex; flex-direction: column;">
                <!-- 概览tab -->
                <div v-if="activeTab === 'overview'" class="reservoir-info">
                    <h4 style="margin: 0 0 16px 0; font-size: 16px; color: #1D2129; font-weight: 600;">水库特征信息</h4>
                    <div class="reservoir-chart">
                        <!-- 水库特征水位示意图 -->
                        <div class="reservoir-diagram">
                          <!-- basic图片 -->
                          <img src="@/assets/images/base.png" class="basic-img" />
                          <!-- 四个特征图片 -->
                          <img src="@/assets/images/drinking-outlets.png" class="feature-img drinking-outlets" />
                          <div class="feature-label drinking-outlets-label">引水口</div>
                          <img src="@/assets/images/overflow-weir-crest.png" class="feature-img overflow-weir-crest" />
                          <div class="feature-label overflow-weir-label">溢流堰顶</div>
                          <img src="@/assets/images/sluice-gate.png" class="feature-img sluice-gate" />
                          <div class="feature-label sluice-gate-label">闸门</div>
                          <img src="@/assets/images/top-sluice.png" class="feature-img top-sluice" />
                          <!-- <div class="feature-label top-sluice-label">顶部大闸</div> -->
                          <!-- 水的示意图 -->
                          <div class="water-animation">
                            <img src="@/assets/images/water.png" class="water-img" />
                          </div>
                          <!-- 固定水位线 -->
                          <div v-for="item in fixedLevels" :key="item.label"
                               class="level-line"
                               :style="{ top: item.top + 'px' }">
                            <span class="level-label" :class="{ 'top-label': item.label === '校验洪水位' }">{{ item.label }} <span class="level-value">{{ item.value }}</span> m</span>
                          </div>
                          <!-- 调度方案水位线 -->
                          <div v-for="item in adjustedSchemeLevels" :key="item.label"
                               class="scheme-line"
                               :style="{ top: item.top + 'px', borderTopColor: item.color }">
                            <span class="scheme-label" :style="{ color: item.color }">{{item.label}} {{item.value}} m</span>
                          </div>
                          <!-- 垂直指示线 -->
                          <div v-for="(line, index) in indicatorLines" :key="index"
                               class="indicator-line"
                               :class="{'right-line': line.position.right}"
                               :style="{ 
                                 ...line.position,
                                 height: line.height
                               }">
                            <img src="@/assets/images/arrow-top.png" class="arrow-img top-arrow" />
                            <img src="@/assets/images/arrow-top.png" class="arrow-img bottom-arrow flip-vertical" />
                            <span class="indicator-label" :class="`${line.labelPosition}-label`">{{line.label.split(' ')[0]}} <span class="indicator-value">{{line.label.split(' ')[1]}}</span> {{line.label.split(' ')[2] || ''}}</span>
                          </div>
                        </div>
                    </div>
                </div>
                <!-- 详情tab -->
                <div v-if="activeTab === 'detail'" style="height: 100%; display: flex; flex-direction: column;">
                  <div style="height: 50%;">
                    <BarAndLineMixChart :dataSource="chartData" />
                  </div>
                  <ResultTable :dataSource="resultData?.resvrDispResList || []" :resultData="resultData" style="height: 50%;" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { getOptions, getValueByKey } from '@/api/common'
    import moment from 'moment'
    import BarAndLineMixChart from './modules/AddModal/BarAndLineMixChart.vue'
    import ResultTable from './modules/AddModal/ResultTable.vue'

    // 水位区间常量
    const MIN_LEVEL = 112.60;
    const MAX_LEVEL = 140.94;
    const CONTAINER_HEIGHT = 400;
    const PX_PER_METER = CONTAINER_HEIGHT / (MAX_LEVEL - MIN_LEVEL);
    
    function getTop(level) {
      return CONTAINER_HEIGHT - (level - MIN_LEVEL) * PX_PER_METER;
    }

    // 根据特征图片位置计算固定水位线位置
    function getFixedLevelsPosition() {
      const containerHeight = 616; // 容器高度
      return [
        { label: '死水位', value: 112.60, top: containerHeight * 0.5 }, // 饮水口最高处
        { label: '灌溉最低水位', value: 115.00, top: containerHeight * 0.5 - 40 }, // 溢流堰顶底部往上50px
        { label: '汛期限制水位', value: 138.60, top: containerHeight * 0.33 - 27 }, // 闸门底部往上27px
        { label: '防洪限制水位', value: 139.60, top: containerHeight * 0.17 }, // 顶部大闸最底部
        { label: '正常水位', value: 140.60, top: containerHeight * 0.17 - 23 }, // 顶部大闸底部往上23px
        { label: '防汛设计水位', value: 140.74, top: containerHeight * 0.17 - 48 }, // 顶部大闸底部往上48px
        { label: '校验洪水位', value: 140.94, top: 0 } // 顶部大闸顶部
      ];
    }
    
    // 根据水位值计算对应的位置
    function getSchemeLevelPosition(level) {
      const containerHeight = 616; // 容器高度
      const fixedLevels = getFixedLevelsPosition();
      
      // 找到最接近的两个固定水位线
      let lowerLevel = null;
      let upperLevel = null;
      
      for (let i = 0; i < fixedLevels.length - 1; i++) {
        if (level >= fixedLevels[i].value && level <= fixedLevels[i + 1].value) {
          lowerLevel = fixedLevels[i];
          upperLevel = fixedLevels[i + 1];
          break;
        }
      }
      
      // 如果找不到合适的区间，使用最接近的固定水位线
      if (!lowerLevel || !upperLevel) {
        if (level <= fixedLevels[0].value) {
          return fixedLevels[0].top;
        } else if (level >= fixedLevels[fixedLevels.length - 1].value) {
          return fixedLevels[fixedLevels.length - 1].top;
        }
      }
      
      // 线性插值计算位置
      const ratio = (level - lowerLevel.value) / (upperLevel.value - lowerLevel.value);
      return lowerLevel.top - ratio * (lowerLevel.top - upperLevel.top);
    }

    export default {
        name: 'DispatchModel',
        components: { BarAndLineMixChart, ResultTable },

        data() {
            return {
                selectedScheme: undefined,
                schemeOptions: [
                    { label: '2024-12-12 历史预演', value: '1' },
                    { label: '2024-12-10 历史预演', value: '2' },
                    { label: '2024-12-08 历史预演', value: '3' }
                ],
                activeTab: 'overview',
                dataSource: null,
                // 来水方案名称区块的信息卡片配置
                waterSchemeCards: [
                    {
                        key: 'rain',
                        title: '累计降雨量',
                        iconClass: 'rain-icon',
                        background: '#E6F5FA',
                        dataKey: 'rainSum',
                        defaultValue: '78.2',
                        unit: 'mm'
                    },
                    {
                        key: 'inWater',
                        title: '累计来水量',
                        iconClass: 'water-icon',
                        background: '#E8EAFF',
                        dataKey: 'inWaterSum',
                        defaultValue: '1245.6',
                        unit: '万m³'
                    }
                ],
                // 出库信息区块的信息卡片配置（按组分组）
                outflowInfoCardGroups: [
                    [
                        {
                            key: 'supply',
                            title: '供水流量',
                            iconClass: 'supply-icon',
                            background: '#E8F3FF',
                            dataKey: 'supplyFlow',
                            defaultValue: '35.6',
                            unit: 'm³/s'
                        },
                        {
                            key: 'discharge',
                            title: '泄洪流量',
                            iconClass: 'discharge-icon',
                            background: '#E2F6F3',
                            dataKey: 'dischargeFlow',
                            defaultValue: '58.9',
                            unit: 'm³/s'
                        }
                    ],
                    [
                        {
                            key: 'totalSupply',
                            title: '累计供水量',
                            iconClass: 'total-supply-icon',
                            background: '#E8EAFF',
                            dataKey: 'totalSupply',
                            defaultValue: '1025.8',
                            unit: '万m³'
                        },
                        {
                            key: 'outflow',
                            title: '累计泄洪量',
                            iconClass: 'outflow-icon',
                            background: '#FFF0E8',
                            dataKey: 'outflowWater',
                            defaultValue: '856.3',
                            unit: '万m³'
                        }
                    ],
                    [
                        {
                            key: 'supplyRate',
                            title: '累计出库水量',
                            iconClass: 'supply-rate-icon',
                            background: '#E6F5FA',
                            dataKey: 'supplyFlowRate',
                            defaultValue: '42.3',
                            unit: '万m³'
                        },
                        {
                            key: 'placeholder',
                            title: '',
                            iconClass: '',
                            background: '#E6F5FA',
                            dataKey: '',
                            defaultValue: '',
                            unit: '',
                            hidden: true
                        }
                    ]
                ],
                // 固定水位线
                fixedLevels: getFixedLevelsPosition(),
                // 调度方案水位线（测试数据）
                schemeLevels: [
                  { label: '最低水位', value: 138.80, color: '#FF7D00' },
                  { label: '起调水位', value: 140.25, color: '#00B42A' },
                  { label: '末期水位', value: 139.40, color: '#14C9C9' },
                  { label: '最高水位', value: 140.80, color: '#F53F3F' }
                ].map(item => ({ ...item, top: getSchemeLevelPosition(item.value) })),
                // 处理后的调度方案水位线
                adjustedSchemeLevels: [],
                // 垂直指示线配置
                indicatorLines: [
                  { position: { right: '20px' }, height: '100%', label: '总库容 704 万m³', labelPosition: 'left' },
                  { position: { left: '640px', bottom: '0' }, height: '50%', label: '死库容 145.57 万m³', labelPosition: 'right' },
                  { position: { left: '640px', bottom: '310px' }, height: '224px', label: '兴利库容 704 万m³', labelPosition: 'right' },
                  { position: { left: '680px', top: '0' }, height: '178px', label: '调洪库容 530 万m³', labelPosition: 'right' },
                  { position: { left: '740px', top: '108px' }, height: '70px', label: '防洪库容 330 万m³', labelPosition: 'right' },
                  { position: { left: '840px', bottom: '0' }, height: '521px', label: '起调库容 704 万m³', labelPosition: 'right' }
                ],
                // 详情tab相关数据
                resultData: null,
                chartData: [],
            }
        },
        computed: {},
        created() {
            // 初始化选择第一个选项
            if (this.schemeOptions.length > 0) {
                this.selectedScheme = this.schemeOptions[0].value;
            }
            this.getSchemeData();
        },
        watch: {
            activeTab(newVal) {
                if (newVal === 'detail') {
                    this.loadDetailData();
                }
            },
        },
        methods: {
            getSchemeData() {
                // 模拟获取方案数据
                // 实际项目中应该调用相应的API
                this.dataSource = {
                    schemeName: '自动预报-桃花江灌区-2025051917~2025052216',
                    forecastPeriod: '2025-05-21 16:00 ~ 2025-05-24 16:00',
                    rainSum: '78.2',
                    inWaterSum: '1245.6',
                    supplyFlow: '35.6',
                    outflowWater: '856.3',
                    totalSupply: '1025.8',
                    dischargeFlow: '58.9',
                    supplyFlowRate: '42.3',
                    // 方案水位数据
                    minLevel: 138.80,
                    startLevel: 140.25,
                    endLevel: 139.40,
                    maxLevel: 140.80
                };
                // 更新方案水位线
                const rawSchemeLevels = [
                  { label: '最低水位', value: this.dataSource.minLevel, color: '#FF7D00' },
                  { label: '起调水位', value: this.dataSource.startLevel, color: '#00B42A' },
                  { label: '末期水位', value: this.dataSource.endLevel, color: '#14C9C9' },
                  { label: '最高水位', value: this.dataSource.maxLevel, color: '#F53F3F' }
                ].map(item => ({ ...item, top: getSchemeLevelPosition(item.value) }));
                
                // 处理相同高度的水位线，避免重叠
                this.adjustedSchemeLevels = this.adjustOverlappingLines(rawSchemeLevels);
            },
            // 处理重叠的水位线
            adjustOverlappingLines(lines) {
                const adjustedLines = [...lines];
                const threshold = 20; // 20px内认为是重叠
                
                // 按top值排序
                adjustedLines.sort((a, b) => a.top - b.top);
                
                for (let i = 1; i < adjustedLines.length; i++) {
                    const current = adjustedLines[i];
                    const previous = adjustedLines[i - 1];
                    
                    // 如果两条线太接近，调整当前线的位置
                    if (Math.abs(current.top - previous.top) < threshold) {
                        // 如果是相同数值，一个上移一个下移
                        if (current.value === previous.value) {
                            previous.top -= 10;
                            current.top += 10;
                        } else {
                            // 否则当前线下移
                            current.top = previous.top + threshold;
                        }
                    }
                }
                
                return adjustedLines;
            },
            goToSchemeManage() {
                this.$router.push('/schedule/dispatch-model-list')
            },
            loadDetailData() {
                // 这里模拟接口数据，实际可调用getResvrDisp({ resvrDispId: ... })
                // 生成模拟resultData和chartData
                const startTime = moment('2025-05-21 16:00', 'YYYY-MM-DD HH:mm');
                const endTime = moment('2025-05-24 16:00', 'YYYY-MM-DD HH:mm');
                const duration = endTime.diff(startTime, 'hours');
                const resvrDispResList = [];
                let totalRainfall = 0;
                for (let i = 0; i <= duration; i++) {
                    const time = startTime.clone().add(i, 'hours').format('YYYY-MM-DD HH:mm');
                    const rain = Math.random() * 10;
                    const inflow = 100 + Math.random() * 50;
                    const supplyFlow = 80 + Math.random() * 40;
                    const floodFlow = 40 + Math.random() * 30;
                    const waterLevel = 85 + Math.random() * 5;
                    totalRainfall += rain;
                    resvrDispResList.push({
                        tm: time,
                        rain: rain.toFixed(1),
                        inflow: inflow.toFixed(1),
                        supplyFlow: supplyFlow.toFixed(1),
                        floodFlow: floodFlow.toFixed(1),
                        wlv: waterLevel.toFixed(2),
                        supplyVolume: (supplyFlow * 0.0036).toFixed(2),
                        floodVolume: (floodFlow * 0.0036).toFixed(2),
                    });
                }
                this.resultData = {
                    caseCode: 'DISP_' + Date.now(),
                    caseName: this.dataSource?.schemeName,
                    startTime: '2025-05-21 16:00',
                    endTime: '2025-05-24 16:00',
                    startWaterLevel: (85 + Math.random() * 2).toFixed(2),
                    endWaterLevel: (87 + Math.random() * 2).toFixed(2),
                    totalRainfall: totalRainfall.toFixed(1),
                    resvrDispResList,
                };
                // 图表数据
                const data = this.resultData.resvrDispResList;
                const rainData = {
                    name: '时段雨量',
                    data: data.map(el => [el.tm, parseFloat(el.rain)]),
                };
                const sumRainData = {
                    name: '累计降雨量',
                    data: rainData.data.map((el, idx) => {
                        const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0);
                        return [el[0], +sum.toFixed(1)];
                    }),
                };
                this.chartData = [
                    rainData,
                    sumRainData,
                    {
                        name: '水位',
                        data: data.map(el => [el.tm, parseFloat(el.wlv)]),
                    },
                    {
                        name: '入库流量',
                        data: data.map(el => [el.tm, parseFloat(el.inflow)]),
                    },
                    {
                        name: '供水流量',
                        data: data.map(el => [el.tm, parseFloat(el.supplyFlow)]),
                    },
                    {
                        name: '泄洪流量',
                        data: data.map(el => [el.tm, parseFloat(el.floodFlow)]),
                    },
                ];
            },
        },
    }
</script>

<style lang="less" scoped>
    .info-panel {
        background-image: linear-gradient(to bottom, rgba(105, 187, 255, 0.15), rgba(64, 196, 249, 0));
        display: flex;
        flex-direction: column;
    }
    
    .block-title {
        font-size: 16px;
        color: #1D2129;
        font-weight: 600;
        margin: 16px 0;
        padding: 0 16px;
    }
    
    .water-scheme, .outflow-info {
        // padding: 0 0 16px 0;
    }
    
    .scheme-info {
        padding: 0 16px;
    }
    
    .info-item {
        margin-bottom: 12px;
        display: flex;
        // align-items: center;
        flex-direction: column;
        
        &.full-width {
            width: 100%;
        }
        
        .label {
            color: #4E5969;
            font-size: 14px;
            margin-right: 8px;
        }
        
        .value {
            color: #1D2129;
            font-size: 14px;
            font-weight: 500;
        }
    }
    
    .info-cards {
        display: flex;
        justify-content: space-between;
        // margin-bottom: 16px;
    }
    .info-cards-bottom {
        padding: 0 16px;
        margin-bottom: 16px;
    }
    
    .info-card {
        width: 48%;
        border-radius: 8px;
        padding: 16px;
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            
            .icon {
                width: 24px;
                height: 24px;
                margin-right: 8px;
                display: inline-block;
            }
            
            .card-title {
                color: #4E5969;
                font-size: 14px;
            }
        }
        
        .card-content {
            .card-value {
                font-size: 20px;
                font-weight: 700;
                color: #1D2129;
            }
            
            .card-unit {
                font-size: 14px;
                color: #4E5969;
                margin-left: 4px;
            }
        }
    }
    
    .reservoir-info {
        height: 100%;
        padding: 16px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
    }
    
    // 图标样式，实际项目中应该使用真实的图标
    .rain-icon {
        background: url('@/assets/images/forecast-cumulative-precipitation.png') 0 0 no-repeat;
        background-size: 100%;
    }
    .water-icon {
        background: url('@/assets/images/cumulative-water-intake.png') 0 0 no-repeat;
        background-size: 100%;
    }
    .supply-icon {
        background: url('@/assets/images/water-supply-flow.png') 0 0 no-repeat;
        background-size: 100%;
    }
    .outflow-icon {
        background: url('@/assets/images/flood-flow.png') 0 0 no-repeat;
        background-size: 100%;
    }
    .total-supply-icon {
        background: url('@/assets/images/cumulative-water-supply.png') 0 0 no-repeat;
        background-size: 100%;
    }
    .discharge-icon {
        background: url('@/assets/images/forecast-out-water.png') 0 0 no-repeat;
        background-size: 100%;
    }
    .supply-rate-icon {
        background: url('@/assets/images/cumulative-flood-discharge.png') 0 0 no-repeat;
        background-size: 100%;
    }

    @font-face {
        font-family: 'AlimamaDaoLiTi';
        src: url('@/assets/font/AlimamaDaoLiTi.ttf');
    }

    .reservoir-chart {
      height: calc(100% - 50px);
      background: #f5f7fa;
      border-radius: 8px;
      position: relative;
      overflow: visible; /* 修改为visible以显示溢出内容 */
    }
    .reservoir-diagram {
      position: relative;
      width: 100%;
      height: 100%;
    }
    .basic-img {
      position: absolute;
      left: 0;
      bottom: 0;
      height: 100%;
      width: 55%;
      z-index: 7;
    }
    .feature-img {
      position: absolute;
      left: 580px;
      z-index: 2;
    }
    .drinking-outlets {
      bottom: 0;
      height: 50%;
    }
    .overflow-weir-crest {
      bottom: 50%;
      left: 592px;
      height: 16.67%;
    }
    .sluice-gate {
      bottom: 67%;
      left: 592px;
      height: 15.67%;
    }
    .top-sluice {
      bottom: 83%;
      left: 592px;
      height: 16.87%;
    }
    .level-line {
      position: absolute;
      left: 320px; /* 从图片左侧开始 */
      width: 270px; /* 只延伸260px */
      height: 1px;
      background: #86909C;
      z-index: 10;
      
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: -2px;
        width: 1px;
        height: 5px;
        background: #86909C;
      }
    }
    .level-label {
      position: absolute;
      left: 0px; /* 向左偏移，使标签显示在线条左侧 */
      top: -22px;
      color: #86909C;
      font-size: 12px;
    //   background: #fff;
      padding: 0 4px;
      border-radius: 2px;
      
      .level-value {
        color: #507EF7;
      }
    }
    .scheme-line {
      position: absolute;
      left: 200px; /* 从图片左侧开始 */
      width: 390px; /* 只延伸260px */
      height: 1px;
      border-top: 1px dashed;
      z-index: 11;
    }
    .scheme-label {
      position: absolute;
      left: 0px; /* 向左偏移，使标签显示在线条左侧 */
      top: -22px;
      font-size: 12px;
      padding: 0 4px;
      border-radius: 2px;
    }
    
    /* 垂直指示线样式 */
    .indicator-line {
      position: absolute;
      width: 1px;
      background: #86909C;
      z-index: 12;
      
      &::before, &::after {
        content: "";
        position: absolute;
        left: -8px;
        width: 16px;
        height: 1px;
        background: transparent;
        border-top: 1px dashed #86909C;
      }
      
      &::before {
        top: 0px; /* 调整位置，让虚线在箭头图标下方 */
      }
      
      &::after {
        bottom: 0px; /* 调整位置，让虚线在箭头图标上方 */
      }
      
      &.right-line {
        right: 50px;
      }
    }
    
    .arrow-img {
      position: absolute;
      width: 8px;
      height: 8px;
      left: -3.5px; /* 居中对齐 */
      z-index: 13;
      
      &.top-arrow {
        top: -2px;
      }
      
      &.bottom-arrow {
        bottom: -2px;
      }
      
      &.flip-vertical {
        transform: rotate(180deg);
      }
    }
    
    .indicator-label {
      position: absolute;
      font-size: 12px;
      color: #86909C;
      white-space: nowrap;
      z-index: 13;
      
      .indicator-value {
        color: #507EF7;
      }
      
      &.left-label {
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
      }
      
      &.right-label {
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
      }
      
      &.top-label {
        left: 50%;
        top: -25px;
        transform: translateX(-50%);
      }
      
      &.bottom-label {
        left: 50%;
        bottom: -25px;
        transform: translateX(-50%);
      }
    }
    
    /* 水的示意图样式 */
    .water-animation {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 619px;
      height: 250px;
      z-index: 5;
    //   border-radius: 50%;
      overflow: hidden;
    //   box-shadow: 0 0 10px rgba(0, 160, 233, 0.3);
    }
    
    .water-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      animation: waterFlow 3s infinite linear;
    }
    
    @keyframes waterFlow {
      0% {
        transform: translateY(0) scale(1);
      }
      50% {
        transform: translateY(-5px) scale(1.05);
      }
      100% {
        transform: translateY(0) scale(1);
      }
    }

    /* 特征图片标签样式 */
    .feature-label {
      position: absolute;
      font-size: 12px;
      color: #1D2129;
      background: rgba(255, 255, 255, 1);
      padding: 2px 6px;
      border-radius: 2px;
      z-index: 8;
      left: 580px;
      border: 1px solid #F2F3F5;
    }

    .drinking-outlets-label {
      bottom: 50%;
      left: 540px;
    }

    .overflow-weir-label {
      bottom: 65%;
      left: 530px;
    }

    .sluice-gate-label {
      bottom: 75%;
      left: 550px;
    }

    // .top-sluice-label {
    //   bottom: 95%;
    //   left: 550px;
    // }
</style>