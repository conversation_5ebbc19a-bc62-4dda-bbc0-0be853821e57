<template>
  <div class="container">
    <a-form-model
      style="margin-top: 24px"
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 8 },
      }"
    >
      <a-form-model-item label="方案名称" prop="caseName">
        <a-input v-model="form.caseName" placeholder="请输入" allow-clear />
      </a-form-model-item>
      <a-form-model-item label="模型应用场景" prop="scene">
        <a-radio-group
          v-model="form.scene"
          :options="sceneOptions.map(el => ({ ...el, label: `${el.label}场景` }))"
          style="margin-top: 5px"
          @change="changeScene"
        />
      </a-form-model-item>
      <a-form-model-item label="预演时间" prop="forecastTime">
        <div style="display: flex; gap: 8px; align-items: center;">
          <a-select
            v-model="form.forecastTimeType"
            style="width: 120px;"
            data-testid="forecast-time-select"
            @change="handleForecastTimeTypeChange"
          >
            <a-select-option value="1">未来1天</a-select-option>
            <a-select-option value="3">未来3天</a-select-option>
            <a-select-option value="7">未来7天</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
          <!-- 预设模式：只显示结束时间 -->
          <a-date-picker
            v-if="form.forecastTimeType !== 'custom'"
            v-model="form.forecastTime"
            disabled
            format="YYYY-MM-DD HH:00"
            valueFormat="YYYY-MM-DD HH:00"
            :show-time="{ format: 'HH' }"
            placeholder="预演结束时间"
            style="flex: 1;"
          />
          <!-- 自定义模式：显示开始时间和结束时间 -->
          <template v-else>
            <a-date-picker
              v-model="form.customStartTime"
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              :disabled-date="disabledCustomStartDate"
              placeholder="开始时间"
              style="flex: 1;"
              @change="handleCustomStartTimeChange"
            />
            <span style="margin: 0 4px;">至</span>
            <a-date-picker
              v-model="form.customEndTime"
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              :disabled-date="disabledCustomEndDate"
              placeholder="结束时间"
              style="flex: 1;"
              @change="handleCustomEndTimeChange"
            />
          </template>
        </div>
      </a-form-model-item>
      <!-- <a-form-model-item label="预报范围" prop="fcstRange">
        <a-radio-group v-model="form.fcstRange" :options="fcstRangeOptions" style="margin-top: 5px" />
      </a-form-model-item> -->
    </a-form-model>
  </div>
</template>

<script>
  import moment from 'moment'
  export default {
    name: 'BasicInfo',
    props: ['fcstRangeOptions', 'sceneOptions'],
    components: {},
    data() {
      return {
        form: {
          caseName: undefined,
          scene: 2, // 默认选中未来预报场景
          fcstRange: this.fcstRangeOptions[0].value,
          forecastTimeType: '3', // 默认选择未来3天
          forecastTime: undefined,
          customStartTime: undefined, // 自定义模式的开始时间
          customEndTime: undefined, // 自定义模式的结束时间
          startTime: undefined, // 保留用于向后兼容
          endTime: undefined, // 保留用于向后兼容
        },
        rules: {
          caseName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          scene: [{ required: true, message: '模型应用场景不能为空', trigger: 'change' }],
          fcstRange: [{ required: true, message: '预报范围不能为空', trigger: 'change' }],
          forecastTime: [
            {
              validator: (rule, value, callback) => {
                if (this.form.forecastTimeType === 'custom') {
                  if (!this.form.customStartTime || !this.form.customEndTime) {
                    callback(new Error('请选择完整的时间区间'))
                  } else {
                    callback()
                  }
                } else {
                  if (!value) {
                    callback(new Error('预演时间不能为空'))
                  } else {
                    callback()
                  }
                }
              },
              trigger: 'change'
            }
          ],
        },
      }
    },
    computed: {},
    created() {
      // 由于默认选中未来预报场景，需要初始化时间
      if (this.form.scene === 2) {
        this.initializeForecastTime()
      }
    },
    methods: {
      changeScene(val) {
        if (val.target.value === 2) {
          this.initializeForecastTime()
          this.$refs.form.validateField('forecastTime')
        } else {
          this.form.forecastTime = undefined
          this.form.customStartTime = undefined
          this.form.customEndTime = undefined
          this.form.startTime = undefined
          this.form.endTime = undefined
        }
      },

      // 初始化预演时间
      initializeForecastTime() {
        const startTime = moment().add(1, 'hours')
        let endTime

        switch (this.form.forecastTimeType) {
          case '1':
            endTime = startTime.clone().add(1, 'days')
            break
          case '3':
            endTime = startTime.clone().add(3, 'days')
            break
          case '7':
            endTime = startTime.clone().add(7, 'days')
            break
          default:
            endTime = startTime.clone().add(3, 'days')
        }

        this.form.forecastTime = endTime.format('YYYY-MM-DD HH:00')
        // 同时更新startTime和endTime用于向后兼容
        this.form.startTime = startTime.format('YYYY-MM-DD HH:00')
        this.form.endTime = this.form.forecastTime
      },

      // 处理预演时间类型变化
      handleForecastTimeTypeChange(value) {
        if (value !== 'custom') {
          // 切换到预设模式，清空自定义时间并初始化预设时间
          this.form.customStartTime = undefined
          this.form.customEndTime = undefined
          this.initializeForecastTime()
        } else {
          // 切换到自定义模式，清空预设时间并初始化自定义时间
          this.form.forecastTime = undefined
          this.initializeCustomTime()
        }
        this.$refs.form.validateField('forecastTime')
      },

      // 初始化自定义时间
      initializeCustomTime() {
        const now = moment()
        this.form.customStartTime = now.add(1, 'hours').format('YYYY-MM-DD HH:00')
        this.form.customEndTime = now.add(3, 'days').format('YYYY-MM-DD HH:00')
      },

      // 处理自定义开始时间变化
      handleCustomStartTimeChange() {
        this.$refs.form.validateField('forecastTime')
      },

      // 处理自定义结束时间变化
      handleCustomEndTimeChange() {
        this.$refs.form.validateField('forecastTime')
      },

      // 自定义开始时间日期禁用逻辑
      disabledCustomStartDate(current) {
        if (this.form.scene === 1) {
          // 历史场景：可选择过去15天到今天
          if (this.form.customEndTime) {
            return (
              current < moment(this.form.customEndTime).subtract(15, 'days').startOf('day') ||
              current > moment(this.form.customEndTime) ||
              current > moment().endOf('day')
            )
          }
          return current > moment().endOf('day')
        } else {
          // 未来预报场景：不能早于今天，不能晚于结束时间
          if (this.form.customEndTime) {
            return (
              current < moment().startOf('day') ||
              current >= moment(this.form.customEndTime)
            )
          }
          return current < moment().startOf('day')
        }
      },

      // 自定义结束时间日期禁用逻辑
      disabledCustomEndDate(current) {
        if (this.form.scene === 1) {
          // 历史场景：可选择开始时间到开始时间+15天，但不能超过今天
          if (this.form.customStartTime) {
            return (
              current <= moment(this.form.customStartTime) ||
              current > moment(this.form.customStartTime).add(15, 'days').endOf('day') ||
              current > moment().endOf('day')
            )
          }
          return current > moment().endOf('day')
        } else {
          // 未来预报场景：不能早于开始时间，最多选择未来10天
          if (this.form.customStartTime) {
            return (
              current <= moment(this.form.customStartTime) ||
              current > moment().add(10, 'days').endOf('day')
            )
          }
          return (
            current < moment().startOf('day') ||
            current > moment().add(10, 'days').endOf('day')
          )
        }
      },
      // 预演时间日期禁用逻辑
      disabledForecastDate(current) {
        if (this.form.scene === 1) {
          // 历史场景：可选择过去15天到今天
          return current < moment().subtract(15, 'days').startOf('day') || current > moment().endOf('day')
        } else {
          // 未来预报场景：可选择今天到未来10天
          return current < moment().startOf('day') || current > moment().add(10, 'days').endOf('day')
        }
      },

      disabledStartDate(current) {
        if (this.form.scene === 1) {
          if (this.form.endTime) {
            return (
              current < moment(this.form.endTime).subtract(15, 'days') ||
              current > moment(this.form.endTime) ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          // 未来预报场景：开始时间虽然被禁用，但逻辑上应该可选择今天及以后
          return current < moment().startOf('day')
        }
      },
      disabledEndDate(current) {
        if (this.form.scene === 1) {
          if (this.form.startTime) {
            return (
              current < moment(this.form.startTime) ||
              current > moment(this.form.startTime).add(15, 'days') ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          // 未来预报场景：可选择今天到今天+3天
          return current < moment().startOf('day') || current > moment().add(3, 'days').endOf('day')
        }
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            // 计算开始时间和结束时间
            const startTime = moment().add(1, 'hours').format('YYYY-MM-DD HH:00')
            const endTime = this.form.forecastTime

            // 构造传递给下一步的数据
            const formData = {
              ...this.form,
              startTime,
              endTime,
              // 添加预演时间区间信息
              forecastTimeRange: {
                type: this.form.forecastTimeType,
                startTime,
                endTime
              }
            }

            this.$emit('saveData', formData)
          } else {
            this.$emit('saveData', false)
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>
